import styled from 'styled-components';
import { motion } from 'framer-motion';

// 首页专用样式组件
// 这些样式组件专门为首页设计，提供更精细的样式控制

// 首页背景容器
export const S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = styled(motion.div)`
  position: relative;
  min-height: 100vh;
  background: ${props => props.theme.yanse.beijing};
  overflow: hidden;
  
  /* 添加微妙的背景纹理 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: ${props => props.theme.mingcheng === 'anhei' 
      ? `radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
         radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
         radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%)`
      : `radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
         radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.05) 0%, transparent 50%),
         radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.05) 0%, transparent 50%)`
    };
    pointer-events: none;
  }
`;

// 首页内容区域
export const Shouyeneirongququyu = styled(motion.div)`
  position: relative;
  z-index: 1;
  padding: ${props => props.theme.jianju.dada} ${props => props.theme.jianju.da};
  max-width: 1400px;
  margin: 0 auto;
  
  @media (max-width: 1200px) {
    max-width: 100%;
    padding: ${props => props.theme.jianju.da} ${props => props.theme.jianju.zhongdeng};
  }
  
  @media (max-width: 768px) {
    padding: ${props => props.theme.jianju.zhongdeng};
  }
`;

// 首页标题区域
export const Shouyebiaotiququyu = styled(motion.div)`
  text-align: center;
  margin-bottom: ${props => props.theme.jianju.chaoda};
  
  @media (max-width: 768px) {
    margin-bottom: ${props => props.theme.jianju.dada};
  }
`;

// 增强版主标题
export const Zengqiangban_zhubiaoti = styled(motion.h1)`
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: ${props => props.theme.ziti.zhongliang.hei};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  margin-bottom: ${props => props.theme.jianju.zhongdeng};
  line-height: 1.2;
  
  /* 添加文字渐变效果 */
  background: ${props => props.theme.mingcheng === 'anhei'
    ? 'linear-gradient(135deg, #ffffff 0%, #e0e7ff 50%, #c7d2fe 100%)'
    : 'linear-gradient(135deg, #1e293b 0%, #475569 50%, #334155 100%)'
  };
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  
  /* 添加文字阴影 */
  text-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? '0 2px 4px rgba(0, 0, 0, 0.3)'
    : '0 2px 4px rgba(0, 0, 0, 0.1)'
  };
`;

// 增强版副标题
export const Zengqiangban_fubiaoti = styled(motion.p)`
  font-size: clamp(1rem, 2.5vw, 1.25rem);
  color: ${props => props.theme.yanse.wenzi_ciyao};
  margin-bottom: ${props => props.theme.jianju.da};
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  opacity: 0.9;
`;

// 功能网格容器
export const Gongnengwanggerongqi = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: ${props => props.theme.jianju.da};
  margin-top: ${props => props.theme.jianju.chaoda};
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: ${props => props.theme.jianju.zhongdeng};
    margin-top: ${props => props.theme.jianju.da};
  }
`;

// 增强版功能卡片
export const Zengqiangban_gongnengkapian = styled(motion.div)`
  position: relative;
  background: ${props => props.theme.yanse.beijing_er};
  border: 1px solid ${props => props.theme.yanse.biankuang};
  border-radius: ${props => props.theme.yuanjiao.da};
  padding: ${props => props.theme.jianju.dada};
  box-shadow: ${props => props.theme.yinying.zhongdeng};
  transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
  cursor: pointer;
  overflow: hidden;
  
  /* 添加悬停时的背景渐变 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: ${props => props.theme.mingcheng === 'anhei'
      ? 'linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%)'
      : 'linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(139, 92, 246, 0.02) 100%)'
    };
    opacity: 0;
    transition: opacity ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
    pointer-events: none;
  }
  
  &:hover {
    transform: translateY(-6px);
    box-shadow: ${props => props.theme.yinying.da};
    border-color: ${props => props.theme.yanse.zhuyao};
    
    &::before {
      opacity: 1;
    }
  }
  
  &:active {
    transform: translateY(-2px);
  }
`;

// 卡片图标区域
export const Kapiantubiaoququyu = styled.div`
  width: 60px;
  height: 60px;
  border-radius: ${props => props.theme.yuanjiao.zhongdeng};
  background: ${props => props.theme.yanse.zhuyao};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: ${props => props.theme.jianju.zhongdeng};
  font-size: 24px;
  color: white;
  
  /* 添加图标渐变背景 */
  background: ${props => props.theme.mingcheng === 'anhei'
    ? 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)'
    : 'linear-gradient(135deg, #1976d2 0%, #7c3aed 100%)'
  };
`;

// 增强版卡片标题
export const Zengqiangban_kapianbiaoti = styled.h3`
  font-size: ${props => props.theme.ziti.daxiao.da};
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  margin-bottom: ${props => props.theme.jianju.zhongdeng};
  position: relative;
  z-index: 1;
`;

// 增强版卡片内容
export const Zengqiangban_kapianneirong = styled.p`
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  color: ${props => props.theme.yanse.wenzi_ciyao};
  line-height: 1.6;
  position: relative;
  z-index: 1;
  opacity: 0.9;
`;

// 底部装饰区域
export const Dibuzhuangshiququyu = styled(motion.div)`
  margin-top: ${props => props.theme.jianju.chaoda};
  text-align: center;
  padding: ${props => props.theme.jianju.dada} 0;
  border-top: 1px solid ${props => props.theme.yanse.biankuang};
  opacity: 0.7;
`;

// 底部文字
export const Dibuwenzi = styled.p`
  font-size: ${props => props.theme.ziti.daxiao.xiao};
  color: ${props => props.theme.yanse.wenzi_ciyao};
  margin: 0;
`;

// 动画预设
export const donghua_yushe = {
  // 容器动画
  rongqi: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: 0.8, ease: 'easeOut' }
  },
  
  // 标题动画
  biaoti: {
    initial: { opacity: 0, y: -30 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 1, delay: 0.2, ease: 'easeOut' }
  },
  
  // 副标题动画
  fubiaoti: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.8, delay: 0.5, ease: 'easeOut' }
  },
  
  // 网格动画
  wangge: {
    initial: { opacity: 0, y: 40 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.8, delay: 0.8, ease: 'easeOut' }
  },
  
  // 卡片动画
  kapian: {
    initial: { opacity: 0, scale: 0.9, y: 20 },
    animate: { opacity: 1, scale: 1, y: 0 },
    whileHover: { scale: 1.02, y: -6 },
    whileTap: { scale: 0.98 }
  }
};
