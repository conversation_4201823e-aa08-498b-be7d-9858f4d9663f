import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../zujian/minganbuju/zhutitiqigong.js';

// 首页容器
const S<PERSON><PERSON><PERSON><PERSON> = styled(motion.div)`
  min-height: 100vh;
  padding-top: calc(64px + ${props => props.theme.jianju.da}); /* 导航栏高度 + 额外间距 */
  padding-left: ${props => props.theme.jianju.da};
  padding-right: ${props => props.theme.jianju.da};
  padding-bottom: ${props => props.theme.jianju.da};
  background: ${props => props.theme.yanse.beijing};
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;

  @media (max-width: 768px) {
    padding-top: calc(56px + ${props => props.theme.jianju.zhongdeng}); /* 移动端导航栏高度 + 额外间距 */
    padding-left: ${props => props.theme.jianju.zhongdeng};
    padding-right: ${props => props.theme.jianju.zhongdeng};
    padding-bottom: ${props => props.theme.jianju.zhongdeng};
  }
`;

// 主标题
const Zhubiaoti = styled(motion.h1)`
  font-size: ${props => props.theme.ziti.daxiao.chaoda};
  font-weight: ${props => props.theme.ziti.zhongliang.hei};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  margin-bottom: ${props => props.theme.jianju.da};
  line-height: ${props => props.theme.ziti.xinggao.jinmi};
  
  @media (max-width: 768px) {
    font-size: ${props => props.theme.ziti.daxiao.dada};
  }
`;

// 副标题
const Fubiaoti = styled(motion.p)`
  font-size: ${props => props.theme.ziti.daxiao.da};
  color: ${props => props.theme.yanse.wenzi_ciyao};
  margin-bottom: ${props => props.theme.jianju.dada};
  max-width: 600px;
  line-height: ${props => props.theme.ziti.xinggao.putong};
  
  @media (max-width: 768px) {
    font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
    margin-bottom: ${props => props.theme.jianju.da};
  }
`;

// 功能卡片容器
const Gongnengkapianrongqi = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: ${props => props.theme.jianju.da};
  max-width: 1200px;
  width: 100%;
  margin-top: ${props => props.theme.jianju.dada};
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: ${props => props.theme.jianju.zhongdeng};
    margin-top: ${props => props.theme.jianju.da};
  }
`;

// 功能卡片
const Gongnengkapian = styled(motion.div)`
  background: ${props => props.theme.yanse.beijing_er};
  border: 1px solid ${props => props.theme.yanse.biankuang};
  border-radius: ${props => props.theme.yuanjiao.da};
  padding: ${props => props.theme.jianju.da};
  box-shadow: ${props => props.theme.yinying.xiao};
  transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
  cursor: pointer;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: ${props => props.theme.yinying.da};
    border-color: ${props => props.theme.yanse.zhuyao};
  }
`;

// 卡片标题
const Kapianbiaoti = styled.h3`
  font-size: ${props => props.theme.ziti.daxiao.da};
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  margin-bottom: ${props => props.theme.jianju.zhongdeng};
`;

// 卡片描述
const Kapianneirong = styled.p`
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  color: ${props => props.theme.yanse.wenzi_ciyao};
  line-height: ${props => props.theme.ziti.xinggao.putong};
`;

// 动画配置
const rongqi_donghua = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: 'easeOut' }
};

const biaoti_donghua = {
  initial: { opacity: 0, y: -20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.8, delay: 0.2, ease: 'easeOut' }
};

const fubiaoti_donghua = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.8, delay: 0.4, ease: 'easeOut' }
};

const kapianrongqi_donghua = {
  initial: { opacity: 0, y: 40 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.8, delay: 0.6, ease: 'easeOut' }
};

const kapian_donghua = {
  initial: { opacity: 0, scale: 0.9 },
  animate: { opacity: 1, scale: 1 },
  whileHover: { scale: 1.02 },
  whileTap: { scale: 0.98 }
};

/**
 * 首页组件
 * 展示网站的主要功能和导航入口
 */
const Shouye = () => {
  const { shifoianheizuti } = useShiyongzhuti();

  // 功能卡片数据
  const gongneng_liebiao = [
    {
      biaoti: '怪物数据',
      neirong: '查看详细的怪物信息，包括属性、技能、掉落物品等数据',
      lianjie: '/guaiwushuju'
    },
    {
      biaoti: '物品数据',
      neirong: '浏览完整的物品资料库，包含装备、道具、材料等信息',
      lianjie: '/wupinshuju'
    },
    {
      biaoti: '地图数据',
      neirong: '探索游戏世界的地图信息，了解各个区域的特色和资源',
      lianjie: '/ditushuju'
    },
    {
      biaoti: '技能数据',
      neirong: '学习各职业的技能详情，制定最佳的技能加点方案',
      lianjie: '/jinengshuju'
    }
  ];

  // 处理卡片点击
  const chuli_kapian_dianji = (lianjie) => {
    window.location.href = lianjie;
  };

  return (
    <Shouyerongqi {...rongqi_donghua}>
      <Zhubiaoti {...biaoti_donghua}>
        欢迎来到 RO百科
      </Zhubiaoti>
      
      <Fubiaoti {...fubiaoti_donghua}>
        您的专业仙境传说资源中心，提供全面的游戏数据查询服务
      </Fubiaoti>
      
      <Gongnengkapianrongqi {...kapianrongqi_donghua}>
        {gongneng_liebiao.map((gongneng, suoyin) => (
          <Gongnengkapian
            key={suoyin}
            {...kapian_donghua}
            transition={{ duration: 0.3, delay: 0.8 + suoyin * 0.1 }}
            onClick={() => chuli_kapian_dianji(gongneng.lianjie)}
          >
            <Kapianbiaoti>{gongneng.biaoti}</Kapianbiaoti>
            <Kapianneirong>{gongneng.neirong}</Kapianneirong>
          </Gongnengkapian>
        ))}
      </Gongnengkapianrongqi>
    </Shouyerongqi>
  );
};

export default Shouye;
